// main.js 文件内容

document.addEventListener("DOMContentLoaded", function() {
    const generateButton = document.getElementById("generate-button");
    const nameInput = document.getElementById("name-input");
    const resultContainer = document.getElementById("result-container");

    generateButton.addEventListener("click", function() {
        const englishName = nameInput.value.trim();
        if (englishName) {
            const chineseNames = generateChineseNames(englishName);
            displayResults(chineseNames);
        } else {
            alert("请输入英文名！");
        }
    });

    function generateChineseNames(englishName) {
        // 这里可以添加更复杂的生成逻辑
        return [
            { name: "李华", meaning: "李：象征着美好，华：代表繁荣。" },
            { name: "张伟", meaning: "张：表示扩展，伟：象征伟大。" },
            { name: "王芳", meaning: "王：象征着权力，芳：代表芬芳。" }
        ];
    }

    function displayResults(names) {
        resultContainer.innerHTML = "";
        names.forEach(item => {
            const nameElement = document.createElement("div");
            nameElement.innerHTML = `<strong>${item.name}</strong>: ${item.meaning}`;
            resultContainer.appendChild(nameElement);
        });
    }
});