:root {
    --primary-color: #d73527;
    --primary-hover: #b8291e;
    --primary-light: rgba(215, 53, 39, 0.1);
    --secondary-color: #f8d7da;
    --background-color: #f8f9fa;
    --card-background: #ffffff;
    --text-color: #2d3748;
    --text-light: #718096;
    --border-color: #e2e8f0;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
    --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);
    --radius: 16px;
    --radius-sm: 8px;
    --spacing: 20px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

* {
    box-sizing: border-box;
}

body {
    font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    background: linear-gradient(135deg, var(--background-color) 0%, #e9ecef 100%);
    background-attachment: fixed;
    margin: 0;
    padding: var(--spacing);
    min-height: 100vh;
    color: var(--text-color);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.container {
    max-width: 600px;
    margin: 40px auto;
    background: var(--card-background);
    padding: 40px;
    border-radius: var(--radius);
    box-shadow: var(--shadow-lg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), #ff6b6b, var(--primary-color));
    background-size: 200% 100%;
    animation: shimmer 3s infinite;
}

.container:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

h1 {
    text-align: center;
    color: var(--text-color);
    font-size: clamp(2em, 5vw, 2.8em);
    margin-bottom: 30px;
    background: linear-gradient(135deg, var(--primary-color), #ff6b6b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    letter-spacing: -0.5px;
}

.input-group {
    margin-bottom: 25px;
    position: relative;
}

input[type="text"] {
    width: 100%;
    padding: 18px var(--spacing);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-sm);
    font-size: 16px;
    font-family: inherit;
    font-weight: 500;
    background: rgba(255, 255, 255, 0.9);
    transition: var(--transition);
    color: var(--text-color);
}

input[type="text"]:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px var(--primary-light), var(--shadow);
    transform: translateY(-1px);
    background: #ffffff;
}

input[type="text"]::placeholder {
    color: var(--text-light);
    font-weight: 400;
    opacity: 0.8;
}

button {
    width: 100%;
    padding: 18px var(--spacing);
    background: linear-gradient(135deg, var(--primary-color), #ff4757);
    color: white;
    border: none;
    border-radius: var(--radius-sm);
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    font-family: inherit;
    letter-spacing: 0.5px;
    transition: var(--transition);
    margin-top: var(--spacing);
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
}

button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s;
}

button:hover::before {
    left: 100%;
}

button:hover {
    background: linear-gradient(135deg, var(--primary-hover), #ff3742);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(215, 53, 39, 0.4);
}

button:active {
    transform: translateY(0);
    box-shadow: 0 4px 15px rgba(215, 53, 39, 0.3);
}

.results {
    margin-top: calc(var(--spacing) * 1.5);
    animation: fadeInUp 0.6s ease-out;
}

.result-item {
    background: linear-gradient(135deg, rgba(248, 215, 218, 0.3), rgba(255, 255, 255, 0.9));
    padding: 24px;
    margin-bottom: var(--spacing);
    border-radius: var(--radius-sm);
    border-left: 4px solid var(--primary-color);
    transition: var(--transition);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.result-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-light), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.result-item:hover::before {
    opacity: 1;
}

.result-item:hover {
    transform: translateX(8px) translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-left-color: var(--primary-hover);
}

.result-item h3 {
    margin: 0 0 12px 0;
    color: var(--primary-color);
    font-size: 1.4em;
    font-weight: 700;
    letter-spacing: -0.3px;
}

.result-item p {
    margin: 0;
    color: var(--text-light);
    line-height: 1.6;
    font-size: 0.95em;
    font-weight: 400;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

@media (max-width: 768px) {
    body {
        padding: 15px;
    }
    
    .container {
        margin: var(--spacing) auto;
        padding: 30px 24px;
        border-radius: var(--radius-sm);
    }
    
    h1 {
        font-size: clamp(1.8em, 6vw, 2.2em);
        margin-bottom: 24px;
    }
    
    input[type="text"] {
        padding: 16px 18px;
        font-size: 16px;
    }
    
    button {
        padding: 16px 18px;
        font-size: 16px;
    }
    
    .result-item {
        padding: 20px;
        margin-bottom: 15px;
    }
}

@media (max-width: 480px) {
    .container {
        margin: 10px;
        padding: 20px 16px;
    }
    
    input[type="text"], button {
        padding: 14px 16px;
        font-size: 15px;
    }
}
