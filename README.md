# 产品网站 README

## 项目简介

该项目是一个帮助外国人生成有趣中文名的网站。用户可以输入他们的英文名，点击生成按钮后，系统将生成三个中文名，并提供每个中文名的中英文寓意解释。

## 文件结构

```
product-website
├── src
│   ├── css
│   │   └── styles.css        # 网站样式文件
│   ├── js
│   │   └── main.js           # 主要JavaScript逻辑文件
│   └── index.html            # 网站主页面
└── README.md                 # 项目文档
```

## 功能

- 用户输入英文名
- 点击生成按钮后，生成三个中文名
- 显示每个中文名的中英文寓意解释

## 使用方法

1. 克隆或下载该项目到本地。
2. 打开 `src/index.html` 文件。
3. 在浏览器中查看和使用网站。

## 运行项目

只需打开 `src/index.html` 文件即可在浏览器中运行该项目。确保您的浏览器支持JavaScript。

## 贡献

欢迎任何形式的贡献！请提交问题或拉取请求。